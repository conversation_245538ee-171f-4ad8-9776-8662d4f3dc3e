#!/usr/bin/env python
"""
Debug script to check the actual database schema for the order_order table.
Run this with: python manage.py shell < debug_database_schema.py
"""

from django.db import connection

def check_order_table_schema():
    """Check what columns actually exist in the order_order table"""
    with connection.cursor() as cursor:
        # Get table schema for PostgreSQL
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'order_order'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        
        print("=== order_order table schema ===")
        print("Column Name | Data Type | Nullable | Default")
        print("-" * 50)
        
        for column in columns:
            column_name, data_type, is_nullable, column_default = column
            print(f"{column_name:<15} | {data_type:<10} | {is_nullable:<8} | {column_default}")
        
        print(f"\nTotal columns: {len(columns)}")
        
        # Check specifically for status-related columns
        status_columns = [col[0] for col in columns if 'status' in col[0].lower()]
        print(f"\nStatus-related columns: {status_columns}")
        
        return columns

if __name__ == "__main__":
    check_order_table_schema()
