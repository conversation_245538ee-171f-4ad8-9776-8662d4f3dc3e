ERROR Staff API Exception: 'AttributeValueProxy' object has no attribute 'for_filtering' for GET /api/staff/products/product-types/3/attribute_values/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\views.py", line 439, in attribute_values
    'for_filtering': attr_value.for_filtering
                     ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AttributeValueProxy' object has no attribute 'for_filtering'
Internal Server Error: /api/staff/products/product-types/3/attribute_values/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\views.py", line 439, in attribute_values
    'for_filtering': attr_value.for_filtering
                     ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AttributeValueProxy' object has no attribute 'for_filtering'
ERROR Internal Server Error: /api/staff/products/product-types/3/attribute_values/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\views.py", line 439, in attribute_values
    'for_filtering': attr_value.for_filtering
                     ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AttributeValueProxy' object has no attribute 'for_filtering'
[18/Jul/2025 18:26:41] "GET /api/staff/products/product-types/3/attribute_values/ HTTP/1.1" 500 123394