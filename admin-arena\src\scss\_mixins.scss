@use './variables' as *;

// Generic flexbox mixin
@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $direction;
  flex-wrap: $wrap;
}

@mixin btn($color, $bg-color) {
  @include flexbox(center, center);
  color: $color;
  background-color: $bg-color;
  padding: 5px 10px;
  border-radius: 3px;
}

// @mixin theme($light-theme: true) {
//   @if $light-theme {
//     background-color: lighten($primary-dark, 100%);
//     color: darken($text-color, 100%);
//   }
// }

// add this class 
// .light {
//   @include theme(true);
//   // @include theme($light-theme: true);
// }


@mixin mobile {
  @media (max-width: $mobile) {
    @content;
  }
}

// Example usage of mobile mixin:
// @include mobile {
//   flex-direction: column;
// }

// Drag and Drop Mixins
@mixin draggable-item {
  cursor: grab;
  transition: all 0.2s ease;

  &:active {
    cursor: grabbing;
  }

  &.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
  }

  &.dropTarget {
    border-color: $primary-500;
    background-color: rgba($primary-500, 0.05);
  }
}

@mixin drag-handle {
  cursor: grab;
  color: $text-placeholder;
  transition: color 0.2s ease;

  &:hover {
    color: $primary-500;
  }

  &:active {
    cursor: grabbing;
  }
}

// Dynamic Mixin for List Styling
@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {
  @for $i from 0 through $max-levels {
    .level-#{$i} {
      margin-left: $i * $gap;
      list-style: circle;
      color: darken($base-color, $i * 10%);

      // Example: Different list styles for different levels
      @if $i % 3==0 {
        list-style: disc;
      }

      @else if $i % 3==1 {
        list-style: square;
      }

      @else {
        list-style: none;
      }

      // Adjust for flex styles
      @if $i ==0 {
        @include flexbox(flex-start, flex-start, row);
        flex-wrap: wrap;
      }

      @else {
        display: block;
      }
    }
  }
}



// Extensions in scss

// Admin-specific mixins for consistent styling

// Enhanced Layout Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

@mixin flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// Text Utilities
@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Button Mixins
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $border-radius;
  font-family: $font-family-sans;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }
}

@mixin button-size($padding-y, $padding-x, $font-size) {
  padding: $padding-y $padding-x;
  font-size: $font-size;
}

// Input Mixins
@mixin input-base {
  display: block;
  width: 100%;
  padding: $spacing-3 $spacing-4;
  border: 1px solid $gray-300;
  border-radius: $border-radius;
  font-family: $font-family-sans;
  font-size: $font-size-sm;
  background-color: white;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: $primary-500;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }

  &:disabled {
    background-color: $gray-50;
    cursor: not-allowed;
  }
}

// Card Mixins
@mixin card {
  background: white;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}

// Table Mixins
@mixin table-base {
  width: 100%;
  border-collapse: collapse;
  background: white;

  th,
  td {
    padding: $spacing-3 $spacing-4;
    text-align: left;
    border-bottom: 1px solid $gray-200;
  }

  th {
    background-color: $gray-50;
    font-weight: $font-weight-semibold;
    font-size: $font-size-sm;
    color: $gray-900;
  }

  tbody tr {
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: $gray-50;
    }
  }
}

// Responsive Mixins
@mixin responsive($breakpoint) {
  @if $breakpoint ==sm {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }

  @if $breakpoint ==md {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }

  @if $breakpoint ==lg {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }

  @if $breakpoint ==xl {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

@mixin mobile-only {
  @media (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}

// Typography Mixins
@mixin text-xs {
  font-size: $font-size-xs;
  line-height: $line-height-tight;
}

@mixin text-sm {
  font-size: $font-size-sm;
  line-height: $line-height-normal;
}

@mixin text-base {
  font-size: $font-size-base;
  line-height: $line-height-normal;
}

@mixin text-lg {
  font-size: $font-size-lg;
  line-height: $line-height-normal;
}

@mixin text-xl {
  font-size: $font-size-xl;
  line-height: $line-height-normal;
}

// Heading Mixins
@mixin heading-sm {
  font-size: $font-size-lg;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
}

@mixin heading-md {
  font-size: $font-size-xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-tight;
}

@mixin heading-lg {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
}

// Button Reset Mixin
@mixin button-reset {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  color: inherit;
  cursor: pointer;
  outline: none;
}

// Page Container Mixin
@mixin page-container {
  max-width: $page-max-width;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

// Edit Product Sections specific mixins
@mixin edit-section-card {
  @include card;
  border: 1px solid $edit-section-border;
  background: $edit-section-bg;
  transition: all 0.2s ease-in-out;

  &:hover {
    background: $edit-section-hover-bg;
  }
}

@mixin edit-form-grid {
  display: grid;
  gap: $edit-form-grid-gap;

  &.two-columns {
    grid-template-columns: 1fr 1fr;

    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }

  &.three-columns {
    grid-template-columns: repeat(3, 1fr);

    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
}

@mixin edit-form-group {
  display: flex;
  flex-direction: column;
  gap: $spacing-2;

  label {
    font-weight: $font-weight-medium;
    font-size: $font-size-sm;
    color: $text-primary;
  }

  .error {
    color: $form-error-color;
    font-size: $font-size-xs;
  }

  .helper-text {
    color: $text-secondary;
    font-size: $font-size-xs;
  }
}

@mixin edit-form-actions {
  @include flex-end;
  gap: $edit-button-spacing;
  padding-top: $edit-form-spacing;
  border-top: 1px solid $edit-section-border;
  margin-top: $edit-form-spacing;
}

@mixin edit-mode-indicator {
  border: 1px solid $edit-mode-border;
  background: $edit-mode-bg;
  box-shadow: $edit-mode-shadow;
}

@mixin form-validation-state($color) {
  border-color: $color;
  box-shadow: 0 0 0 3px rgba($color, 0.1);
}

@mixin section-header {
  @include flex-between;
  margin-bottom: $spacing-6;

  .title-section {

    h1,
    h2,
    h3 {
      margin: 0 0 $spacing-1 0;
      color: $text-primary;
    }

    p {
      margin: 0;
      color: $text-secondary;
      font-size: $font-size-sm;
    }
  }
}

// Drag and Drop Mixins
@mixin draggable-item {
  cursor: move;
  transition: $transition-all;

  &.dragging {
    opacity: $drag-opacity;
    transform: rotate($drag-rotation);
    z-index: $z-10;
  }

  &.drop-target {
    border-color: $drop-target-border !important;
    background-color: $drop-target-bg !important;
    box-shadow: $drop-target-shadow !important;
  }
}

@mixin drag-handle {
  cursor: grab;
  color: $text-placeholder;
  transition: color 0.2s ease;

  &:hover {
    color: $text-secondary;
  }

  &:active {
    cursor: grabbing;
  }
}

// Image Preview Mixins
@mixin image-thumbnail {
  width: $image-thumbnail-size;
  height: $image-thumbnail-size;
  object-fit: cover;
  border-radius: $border-radius;
  border: 1px solid $border-color;

  @include mobile-only {
    width: $image-thumbnail-mobile-size;
    height: $image-thumbnail-mobile-size;
  }
}

@mixin image-preview-large {
  max-width: 100%;
  max-height: $image-preview-max-height;
  object-fit: contain;
  border-radius: $border-radius;
  box-shadow: $shadow-lg;

  @include mobile-only {
    max-height: $image-preview-mobile-max-height;
  }
}

@mixin image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-sm;
  opacity: 0;
  transition: opacity 0.2s ease;

  .overlay-button {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: $border-radius-full;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: white;
      transform: scale(1.1);
    }
  }
}

// Modal Mixins
@mixin modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: $modal-backdrop-z;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-4;
}

@mixin modal-content {
  background: white;
  border-radius: $modal-border-radius;
  box-shadow: $modal-shadow;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  z-index: $modal-z;
}

@mixin modal-header {
  padding: $spacing-6 $spacing-6 $spacing-4;
  border-bottom: 1px solid $border-light;

  h2,
  h3 {
    margin: 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }
}

@mixin modal-body {
  padding: $spacing-6;
}

@mixin modal-footer {
  padding: $spacing-4 $spacing-6 $spacing-6;
  border-top: 1px solid $border-light;
  display: flex;
  justify-content: flex-end;
  gap: $spacing-3;
}

// React Select Mixins
@mixin react-select-base {
  .react-select__control {
    @include input-base;
    border: 1px solid $react-select-border !important;
    box-shadow: none !important;
    min-height: 40px;

    &:hover {
      border-color: $react-select-focus-border !important;
    }

    &--is-focused {
      border-color: $react-select-focus-border !important;
      box-shadow: 0 0 0 1px $react-select-focus-border !important;
    }
  }

  .react-select__multi-value {
    background-color: $react-select-multi-value-bg;
    border-radius: $border-radius-sm;
  }

  .react-select__multi-value__label {
    color: $react-select-multi-value-text;
    font-size: $font-size-sm;
  }

  .react-select__multi-value__remove {
    color: $react-select-multi-value-remove;

    &:hover {
      background-color: $react-select-multi-value-remove-hover-bg;
      color: $react-select-multi-value-remove-hover-text;
    }
  }

  .react-select__placeholder {
    color: $react-select-placeholder;
    font-size: $font-size-sm;
  }

  .react-select__menu {
    border: 1px solid $border-color;
    box-shadow: $react-select-menu-shadow;
  }

  .react-select__option {
    font-size: $font-size-sm;

    &--is-focused {
      background-color: $primary-50;
    }

    &--is-selected {
      background-color: $primary-500;
    }
  }
}