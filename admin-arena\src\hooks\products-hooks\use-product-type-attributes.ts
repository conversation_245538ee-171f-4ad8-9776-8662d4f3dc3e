import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ProductService } from '../../services/product-service';
import { useNotifications } from '../../stores/ui-store';
import { queryKeys } from '../../services/query-keys';
import type { ProductTypeAttribute, ProductTypeAttributeAssociation } from '../../types/api-types';

/**
 * Hook for fetching attributes associated with a product type
 */
export const useProductTypeAttributes = (productTypeId: number) => {
  return useQuery({
    queryKey: queryKeys.productTypes.attributes(productTypeId),
    queryFn: () => ProductService.getProductTypeAttributes(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook for associating attributes with a product type
 */
export const useAssociateProductTypeAttributes = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (data: { productTypeId: number; attributeIds: number[] }) =>
      ProductService.associateProductTypeAttributes(data.productTypeId, data.attributeIds),
    onSuccess: (_, { productTypeId }) => {
      showSuccess('Attributes Associated', 'Attributes have been associated with the product type successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.productTypes.attributes(productTypeId) });
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate attributes with product type.');
    },
  });
};

/**
 * Hook for updating product type attribute association
 */
export const useUpdateProductTypeAttribute = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: ({
      productTypeId,
      attributeId,
      data,
    }: {
      productTypeId: number;
      attributeId: number;
      data: Partial<ProductTypeAttribute>;
    }) => ProductService.updateProductTypeAttribute(productTypeId, attributeId, data),
    onSuccess: (updatedAssociation, { productTypeId }) => {
      showSuccess('Attribute Updated', 'Product type attribute has been updated successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.productTypes.attributes(productTypeId) });
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type attribute.');
    },
  });
};

/**
 * Hook for removing an attribute from a product type
 */
export const useRemoveProductTypeAttribute = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useNotifications();

  return useMutation({
    mutationFn: (data: { productTypeId: number; attributeId: number }) =>
      ProductService.removeProductTypeAttribute(data.productTypeId, data.attributeId),
    onSuccess: (_, { productTypeId }) => {
      showSuccess('Attribute Removed', 'Attribute has been removed from the product type successfully.');
      queryClient.invalidateQueries({ queryKey: queryKeys.productTypes.attributes(productTypeId) });
    },
    onError: (error: any) => {
      showError('Removal Failed', error.message || 'Failed to remove attribute from product type.');
    },
  });
};
