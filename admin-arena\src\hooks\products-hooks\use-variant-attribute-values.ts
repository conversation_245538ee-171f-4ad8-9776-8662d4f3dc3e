import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../../services/product-service'
import { useNotifications } from '../../stores/ui-store'

/**
 * Hook for fetching variant attribute values by variant
 */
export const useVariantAttributeValuesByVariant = (variantId: number) => {
  return useQuery({
    queryKey: ['variant-attribute-values', variantId],
    queryFn: () => ProductService.getVariantAttributeValuesByVariant(variantId),
    enabled: !!variantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for updating variant attribute value association
 */
export const useUpdateVariantAttributeValueDetails = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: { is_active?: boolean } }) =>
      ProductService.updateVariantAttributeValue(id, data),
    onSuccess: () => {
      showSuccess('Attribute Association Updated', 'Attribute value association has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute association.')
    },
  })
}

/**
 * Hook for individual variant attribute value reorder
 */
export const useReorderVariantAttributeValue = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ associationId, newOrder }: { associationId: number; newOrder: number }) =>
      ProductService.reorderVariantAttributeValue(associationId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Attribute value has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder attribute value.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering variant attribute values
 */
export const useReorderVariantAttributeValuesDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productVariantId, orderedIds }: { productVariantId: number; orderedIds: number[] }) =>
      ProductService.reorderVariantAttributeValuesDragDrop(productVariantId, orderedIds),
    onSuccess: () => {
      showSuccess('Reordered', 'Attribute values have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder attribute values.')
    },
  })
}

/**
 * Hook for bulk updating variant attribute values order
 */
export const useBulkUpdateVariantAttributeValuesOrder = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (orderUpdates: { id: number; order: number }[]) =>
      ProductService.bulkUpdateVariantAttributeValuesOrder(orderUpdates),
    onSuccess: () => {
      showSuccess('Updated', 'Attribute values order has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: ['variant-attribute-values'] })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update attribute values order.')
    },
  })
}