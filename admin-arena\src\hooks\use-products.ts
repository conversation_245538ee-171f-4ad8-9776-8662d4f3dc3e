// Products hooks using TanStack Query
// Provides reactive product data management

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ProductService } from '../services/product-service'
import { useNotifications } from '../stores/ui-store'
import { queryKeys } from '../services/query-keys'
import type {
  Product,
  ProductFilters,
  ProductTypeAttributeAssociation,
  BrandProductType
} from '../types/api-types'

/**
 * Hook for fetching paginated products list
 */
export const useProducts = (filters?: ProductFilters) => {
  return useQuery({
    queryKey: queryKeys.products.list(filters),
    queryFn: () => ProductService.getProducts(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    // placeholderData: (previousData) => previousData,
  })
}

/**
 * Hook for fetching single product
 */
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.detail(id),
    queryFn: () => ProductService.getProduct(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for fetching full product details including variants, images, and attribute values
 */
export const useFullProductDetails = (id: number) => {
  return useQuery({
    queryKey: queryKeys.products.fullDetail(id),
    queryFn: () => ProductService.getFullProductDetails(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for creating new product
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.createProduct,
    onSuccess: (newProduct) => {
      showSuccess('Product Created', `${newProduct.name} has been created successfully.`)

      // Invalidate products list
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })

      // Add to cache
      queryClient.setQueryData(
        queryKeys.products.detail(newProduct.id),
        newProduct
      )
    },
    onError: (error: any) => {
      showError('Creation Failed', error.message || 'Failed to create product.')
    },
  })
}

/**
 * Hook for updating product
 */
export const useUpdateProduct = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Product>) => ProductService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      showSuccess('Product Updated', `${updatedProduct.name} has been updated successfully.`)

      // Update cached data
      queryClient.setQueryData(
        queryKeys.products.detail(id),
        updatedProduct
      )

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product.')
    },
  })
}

/**
 * Hook for updating product details only
 */
export const useUpdateProductDetails = (id: number) => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: (data: Partial<Product>) => ProductService.updateProduct(id, data),
    onSuccess: (updatedProduct) => {
      showSuccess('Product Details Updated', 'Product details have been updated successfully.')

      // Update cached data
      queryClient.setQueryData(
        queryKeys.products.detail(id),
        updatedProduct
      )
      queryClient.setQueryData(
        queryKeys.products.fullDetail(id),
        updatedProduct
      )

      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product details.')
    },
  })
}

// Product variant hooks have been moved to use-product-variants.ts
// Product image hooks have been moved to use-product-images.ts
// Variant attribute value hooks have been moved to use-variant-attribute-values.ts

/**
 * Hook for deleting product
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteProduct,
    onSuccess: (_, productId) => {
      showSuccess('Product Deleted', 'Product has been deleted successfully.')

      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.products.detail(productId) })

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.products.lists() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete product.')
    },
  })
}

/**
 * Hook for bulk product operations
 */
export const useBulkProductOperation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkOperation,
    onSuccess: (result, { action, productIds }) => {
      const count = productIds.length
      showSuccess(
        'Bulk Operation Complete',
        `Successfully ${action} ${count} product${count > 1 ? 's' : ''}.`
      )

      // Invalidate all product queries
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Bulk Operation Failed', error.message || 'Failed to complete bulk operation.')
    },
  })
}

/**
 * Hook for product variants
 */
export const useProductVariants = (productId: number) => {
  return useQuery({
    queryKey: queryKeys.products.variants(productId),
    queryFn: () => ProductService.getProductVariants(productId),
    enabled: !!productId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
      ProductService.updateVariant(id, data),
    onSuccess: (updatedVariant) => {
      showSuccess('Variant Updated', `${updatedVariant.sku} has been updated successfully.`)
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update variant.')
    },
  })
}

/**
 * Hook for deleting variant
 */
export const useDeleteVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteVariant,
    onSuccess: () => {
      showSuccess('Variant Deleted', 'Variant has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete variant.')
    },
  })
}

/**
 * Hook for updating category
 */
// export const useUpdateCategory = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
//       ProductService.updateCategory(id, data),
//     onSuccess: (updatedCategory) => {
//       showSuccess('Category Updated', `${updatedCategory.name} has been updated successfully.`)
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() })
//     },
//     onError: (error: any) => {
//       showError('Update Failed', error.message || 'Failed to update category.')
//     },
//   })
// }

/**
 * Hook for deleting category
 */
// export const useDeleteCategory = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ProductService.deleteCategory,
//     onSuccess: () => {
//       showSuccess('Category Deleted', 'Category has been deleted successfully.')
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.categories() })
//     },
//     onError: (error: any) => {
//       showError('Deletion Failed', error.message || 'Failed to delete category.')
//     },
//   })
// }

// /**
//  * Hook for creating product
//  */
// export const useCreateProduct = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ProductService.createProduct,
//     onSuccess: (newProduct) => {
//       showSuccess('Product Created', `${newProduct.title} has been created successfully.`)
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
//     },
//     onError: (error: any) => {
//       showError('Creation Failed', error.message || 'Failed to create product.')
//     },
//   })
// }

/**
 * Hook for updating brand
 */
// export const useUpdateBrand = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ({ id, data }: { id: number; data: Partial<any> }) =>
//       ProductService.updateBrand(id, data),
//     onSuccess: (updatedBrand) => {
//       showSuccess('Brand Updated', `${updatedBrand.title} has been updated successfully.`)
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
//     },
//     onError: (error: any) => {
//       showError('Update Failed', error.message || 'Failed to update brand.')
//     },
//   })
// }

/**
 * Hook for deleting brand
 */
// export const useDeleteBrand = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ProductService.deleteBrand,
//     onSuccess: () => {
//       showSuccess('Brand Deleted', 'Brand has been deleted successfully.')
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
//     },
//     onError: (error: any) => {
//       showError('Deletion Failed', error.message || 'Failed to delete brand.')
//     },
//   })
// }

/**
 * Hook for fetching product types associated with a brand
 */
export const useBrandProductTypes = (brandId: number) => {
  return useQuery({
    queryKey: ['brand-product-types', brandId],
    queryFn: () => ProductService.getBrandProductTypes(brandId),
    enabled: !!brandId,
    staleTime: 10 * 60 * 1000,
  })
}

/**
 * Hook for setting product types for a brand
 */
export const useSetBrandProductTypes = () => {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: ({ brandId, productTypeIds }: { brandId: number; productTypeIds: number[] }) =>
      ProductService.setBrandProductTypes(brandId, productTypeIds),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['brand-product-types', variables.brandId] })
    },
  })
}

/**
 * Hook for deleting product type
 */
// export const useDeleteProductType = () => {
//   const queryClient = useQueryClient()
//   const { showSuccess, showError } = useNotifications()

//   return useMutation({
//     mutationFn: ProductService.deleteProductType,
//     onSuccess: (_, productTypeId) => {
//       showSuccess('Product Type Deleted', 'Product type has been deleted successfully.')
//       queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
//     },
//     onError: (error: unknown) => {
//       if (error && typeof error === 'object' && 'message' in error) {
//         // @ts-expect-error: message might exist
//         showError('Deletion Failed', error.message || 'Failed to delete product type.')
//       } else {
//         showError('Deletion Failed', 'Failed to delete product type.')
//       }
//     },
//   })
// }

// Brand-Product Type Association Hooks

/**
 * Hook for fetching all brand-product type associations
 */
// export const useBrandProductTypeAssociations = () => {
//   return useQuery({
//     queryKey: queryKeys.products.brandProductTypes(),
//     queryFn: ProductService.getBrandProductTypeAssociations,
//     staleTime: 10 * 60 * 1000, // 10 minutes
//   })
// }

/**
 * Hook for bulk associating brands with product types
 */
export const useBulkAssociateBrandProductTypes = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.bulkAssociateBrandProductTypes,
    onSuccess: () => {
      showSuccess('Associations Created', 'Brand-Product Type associations have been created successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to create brand-product type associations.')
    },
  })
}

/**
 * Hook for deleting brand-product type association
 */
export const useDeleteBrandProductTypeAssociation = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ProductService.deleteBrandProductTypeAssociation,
    onSuccess: () => {
      showSuccess('Association Deleted', 'Brand-Product Type association has been deleted successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brandProductTypes() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.brands() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Deletion Failed', error.message || 'Failed to delete brand-product type association.')
    },
  })
}

// Product Type-Attribute Association Hooks

/**
 * Hook for fetching attributes associated with a product type
 */
export const useProductTypeAttributes = (productTypeId: number) => {
  return useQuery({
    queryKey: queryKeys.products.productTypeAttributes(productTypeId),
    queryFn: () => ProductService.getProductTypeAttributes(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Hook for associating attributes with a product type
 */
export const useAssociateProductTypeAttributes = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, attributes }: {
      productTypeId: number
      attributes: ProductTypeAttributeAssociation[]
    }) => ProductService.associateProductTypeAttributes(productTypeId, attributes),
    onSuccess: (_, variables) => {
      showSuccess('Attributes Associated', 'Attributes have been associated with the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.attributes() })
    },
    onError: (error: any) => {
      showError('Association Failed', error.message || 'Failed to associate attributes with product type.')
    },
  })
}

/**
 * Hook for updating product type attribute association
 */
export const useUpdateProductTypeAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, attributeId, data }: {
      productTypeId: number
      attributeId: number
      data: Partial<ProductTypeAttributeAssociation>
    }) => ProductService.updateProductTypeAttribute(productTypeId, attributeId, data),
    onSuccess: (_, variables) => {
      showSuccess('Association Updated', 'Product type attribute association has been updated successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
    },
    onError: (error: any) => {
      showError('Update Failed', error.message || 'Failed to update product type attribute association.')
    },
  })
}

/**
 * Hook for removing attribute from product type
 */
export const useRemoveProductTypeAttribute = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productTypeId, attributeId }: { productTypeId: number; attributeId: number }) =>
      ProductService.removeProductTypeAttribute(productTypeId, attributeId),
    onSuccess: (_, variables) => {
      showSuccess('Attribute Removed', 'Attribute has been removed from the product type successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.productTypeAttributes(variables.productTypeId) })
      queryClient.invalidateQueries({ queryKey: queryKeys.products.types() })
    },
    onError: (error: any) => {
      showError('Removal Failed', error.message || 'Failed to remove attribute from product type.')
    },
  })
}

// Variant attribute value hooks have been moved to use-variant-attribute-values.ts

// Product creation with variants hooks have been moved to use-product-creation.ts

// Product Image Hooks have been moved to use-product-images.ts

// Product Variants Hooks have been moved to use-product-variants.ts

/**
 * Hook for fetching attribute values by product type
 */
export const useAttributeValuesByProductType = (productTypeId: number) => {
  return useQuery({
    queryKey: ['product-type-attribute-values', productTypeId],
    queryFn: () => ProductService.getAttributeValuesByProductType(productTypeId),
    enabled: !!productTypeId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Variant attribute value hooks have been moved to use-variant-attribute-values.ts

/**
 * Hook for individual product variant reorder
 */
export const useReorderProductVariant = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ variantId, newOrder }: { variantId: number; newOrder: number }) =>
      ProductService.reorderProductVariant(variantId, newOrder),
    onSuccess: () => {
      showSuccess('Reordered', 'Product variant has been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product variant.')
    },
  })
}

/**
 * Hook for drag-and-drop reordering product variants
 */
export const useReorderProductVariantsDragDrop = () => {
  const queryClient = useQueryClient()
  const { showSuccess, showError } = useNotifications()

  return useMutation({
    mutationFn: ({ productId, orderedIds }: { productId: number; orderedIds: number[] }) =>
      ProductService.reorderProductVariantsDragDrop(productId, orderedIds),
    onSuccess: () => {
      showSuccess('Reordered', 'Product variants have been reordered successfully.')
      queryClient.invalidateQueries({ queryKey: queryKeys.products.all })
    },
    onError: (error: any) => {
      showError('Reorder Failed', error.message || 'Failed to reorder product variants.')
    },
  })
}

