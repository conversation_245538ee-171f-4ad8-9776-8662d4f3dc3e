from django.contrib import admin
from apps.customers.models import Address
from .models import Order, OrderItem


class AddressInline(admin.StackedInline):
    model = Address
    extra = 0


class OrderItemInline(admin.StackedInline):
    model = OrderItem
    inlines = [
        AddressInline
    ]
    list_display = ['id', 'order', 'product', 'product_variant ', 'total_price', 'quantity',
                    'extra_data']
    readonly_fields = ['id', 'order', 'product', 'product_variant', 'total_price', 'quantity',
                       'extra_data']
    extra = 0


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'payment_status', 'payment_method', 'order_status',
                    'total', 'placed_at', 'customer', 'selected_address_id', 'shipping_cost',
                    'subtotal', 'total_weight']
    ordering = ['id', 'payment_status', 'payment_method', 'order_status', 'shipping_cost',
                'subtotal', 'total_weight', 'placed_at', 'customer']
    list_filter = ['order_status', 'placed_at']
    # search_fields = ['customer__first_name', 'customer__last_name']
    readonly_fields = ['customer', 'payment_status', 'selected_address', 'total',
                       'placed_at', 'payment_method', 'shipping_cost', 'total_weight',
                       'subtotal', 'payment_intent_id']
    # If you want to control which fields appear in the admin form
    # fields = ['payment_status', 'order_status', 'payment_method', 'selected_address',
    #           'customer', 'placed_at', 'total']

    # readonly_fields = []
    inlines = [
        OrderItemInline,
        # AddressInline
    ]

    #  Readonly only when editing an existing object (not when creating a new one)
    # def get_readonly_fields(self, request, obj=None):
    #     if obj:  # editing an existing object
    #         return self.readonly_fields + ['customer', 'placed_at']
    #     return self.readonly_fields

# @admin.register(BraintreeTransaction)
# class BraintreeTransactionAdmin(admin.ModelAdmin):
#     list_display = ['id', 'order', 'transaction_id', 'amount', 'status', 'payment_method', 'created_at']
#     list_filter = ['status', 'payment_method', 'created_at']
#     search_fields = ['transaction_id', 'order__id']
#     ordering = ['-created_at']
#     readonly_fields = ['id', 'order', 'transaction_id', 'amount', 'status', 'payment_method', 'created_at']
#     fields = ['order', 'transaction_id', 'amount', 'status', 'payment_method', 'created_at']
