from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from decimal import Decimal
import logging
import hashlib
from django.core.cache import cache
from django.utils import timezone
from .packing import PackingResult


@dataclass
class ShippingRate:
    """Represents a shipping rate from a carrier"""
    carrier_name: str
    service_name: str
    cost: Decimal
    estimated_days: int
    max_days: int
    tracking_available: bool = True
    insurance_available: bool = False
    signature_available: bool = False
    carrier_id: Optional[int] = None
    service_id: Optional[int] = None


class ShippingService:
    """Service for calculating shipping costs using multiple carriers"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache_timeout = 300  # 5 minutes
        self.cache_prefix = 'shipping:rates:'
        self.carriers = None
    
    def calculate_shipping_cost(self, packing_result: PackingResult, 
                              destination_address) -> Optional[ShippingRate]:
        """Calculate best shipping rate from available carriers"""
        
        # Generate cache key
        cache_key = self._generate_cache_key(packing_result, destination_address)
        cached_rate = cache.get(cache_key)
        
        if cached_rate:
            self.logger.debug(f"Using cached shipping rate: {cached_rate.cost}")
            return cached_rate
        
        # Load carriers if not already loaded
        if self.carriers is None:
            self.carriers = self._load_active_carriers()
        
        if not self.carriers:
            self.logger.warning("No active carriers available")
            return self._get_fallback_rate(packing_result)
        
        # Get rates from all carriers
        rates = []
        for carrier in self.carriers:
            try:
                rate = carrier.get_shipping_rate(packing_result, destination_address)
                if rate:
                    rates.append(rate)
            except Exception as e:
                self.logger.warning(f"Carrier {carrier.__class__.__name__} failed: {e}")
                continue
        
        if not rates:
            self.logger.warning("No carriers returned valid rates")
            return self._get_fallback_rate(packing_result)
        
        # Select best rate (lowest cost)
        best_rate = min(rates, key=lambda r: r.cost)
        
        # Cache the result
        cache.set(cache_key, best_rate, self.cache_timeout)
        
        self.logger.info(f"Best shipping rate: {best_rate.carrier_name} - ${best_rate.cost}")
        return best_rate
    
    def get_all_shipping_rates(self, packing_result: PackingResult, 
                             destination_address) -> List[ShippingRate]:
        """Get shipping rates from all available carriers"""
        
        if self.carriers is None:
            self.carriers = self._load_active_carriers()
        
        rates = []
        for carrier in self.carriers:
            try:
                rate = carrier.get_shipping_rate(packing_result, destination_address)
                if rate:
                    rates.append(rate)
            except Exception as e:
                self.logger.warning(f"Carrier {carrier.__class__.__name__} failed: {e}")
                continue
        
        # Sort by cost
        rates.sort(key=lambda r: r.cost)
        return rates
    
    def _load_active_carriers(self):
        """Load and initialize active carriers"""
        from .carriers import get_carrier_instance
        from ..models import Carrier
        
        carriers = []
        carrier_configs = Carrier.objects.filter(is_active=True).order_by('-priority')
        
        for carrier_config in carrier_configs:
            try:
                carrier_instance = get_carrier_instance(carrier_config)
                if carrier_instance:
                    carriers.append(carrier_instance)
                    self.logger.info(f"Loaded carrier: {carrier_config.title}")
            except Exception as e:
                self.logger.error(f"Failed to load carrier {carrier_config.title}: {e}")
        
        return carriers
    
    def _generate_cache_key(self, packing_result: PackingResult, destination_address):
        """Generate cache key for shipping rate"""
        # Create a hash of the key components
        key_data = {
            'total_weight': str(packing_result.total_weight),
            'total_volume': str(packing_result.total_volume),
            'box_count': len(packing_result.boxes),
            'country': getattr(destination_address, 'country', 'US'),
            'postal_code': getattr(destination_address, 'postal_code', ''),
        }
        
        key_string = '|'.join(f"{k}:{v}" for k, v in sorted(key_data.items()))
        key_hash = hashlib.md5(key_string.encode()).hexdigest()[:16]
        
        return f"{self.cache_prefix}{key_hash}"
    
    def _get_fallback_rate(self, packing_result: PackingResult) -> ShippingRate:
        """Get fallback shipping rate when no carriers are available"""
        # Simple weight-based fallback calculation
        total_weight = float(packing_result.total_weight)
        
        if total_weight <= 250:
            cost = Decimal('8.50')
        elif total_weight <= 500:
            cost = Decimal('10.50')
        elif total_weight <= 1000:
            cost = Decimal('12.50')
        elif total_weight <= 2000:
            cost = Decimal('15.50')
        elif total_weight <= 5000:
            cost = Decimal('19.50')
        else:
            cost = Decimal('25.50')
        
        # Add box handling fee
        box_fee = len(packing_result.boxes) * Decimal('2.00')
        total_cost = cost + box_fee
        
        return ShippingRate(
            carrier_name="Standard Shipping",
            service_name="Fallback Service",
            cost=total_cost,
            estimated_days=5,
            max_days=7,
            tracking_available=False,
            insurance_available=False,
            signature_available=False
        )
    
    def invalidate_cache(self, destination_address=None):
        """Invalidate shipping rate cache"""
        if destination_address:
            # Invalidate specific cache entries (would need more complex implementation)
            pass
        else:
            # Invalidate all shipping rate cache
            cache.delete_many(cache.keys(f"{self.cache_prefix}*"))
    
    def get_carrier_status(self):
        """Get status of all configured carriers"""
        from ..models import Carrier
        
        status = []
        for carrier_config in Carrier.objects.all():
            try:
                from .carriers import get_carrier_instance
                carrier_instance = get_carrier_instance(carrier_config)
                
                # Test carrier connection
                test_result = carrier_instance.test_connection() if hasattr(carrier_instance, 'test_connection') else True
                
                status.append({
                    'id': carrier_config.id,
                    'title': carrier_config.title,
                    'code': carrier_config.code,
                    'is_active': carrier_config.is_active,
                    'status': 'online' if test_result else 'offline',
                    'last_checked': timezone.now()
                })
            except Exception as e:
                status.append({
                    'id': carrier_config.id,
                    'title': carrier_config.title,
                    'code': carrier_config.code,
                    'is_active': carrier_config.is_active,
                    'status': 'error',
                    'error': str(e),
                    'last_checked': timezone.now()
                })
        
        return status


class ShippingCalculationError(Exception):
    """Exception raised when shipping calculation fails"""
    pass


class CarrierError(Exception):
    """Exception raised when carrier operation fails"""
    pass
