# Product Type Attribute Association Management API

## Overview
This document demonstrates how to use the RESTful endpoints to manage ProductTypeAttribute associations.

## Endpoint Details

### Get Association
**URL:** `/api/staff/products/associations/{association_id}/`
**Method:** `GET`
**Permission:** `CanManageAttributes`
**Description:** Retrieve a specific product type attribute association

### Update Association
**URL:** `/api/staff/products/associations/{association_id}/`
**Method:** `PATCH`
**Permission:** `CanManageAttributes`
**Description:** Update existing product type attribute association settings (is_filterable and is_option_selector)

### Delete Association
**URL:** `/api/staff/products/associations/{association_id}/`
**Method:** `DELETE`
**Permission:** `CanManageAttributes`
**Description:** Delete a product type attribute association

## Request Format

### Update Fields (at least one must be provided)
- `is_filterable` (boolean): Whether the attribute should be filterable
- `is_option_selector` (boolean): Whether the attribute should be an option selector

## Usage Examples

### Example 1: Get association details
```json
GET /api/staff/products/associations/123/
```

### Example 2: Update both fields
```json
PATCH /api/staff/products/associations/123/
{
  "is_filterable": true,
  "is_option_selector": false
}
```

### Example 3: Partial update (only is_filterable)
```json
PATCH /api/staff/products/associations/123/
{
  "is_filterable": true
}
```

### Example 4: Partial update (only is_option_selector)
```json
PATCH /api/staff/products/associations/123/
{
  "is_option_selector": true
}
```

### Example 5: Delete association
```json
DELETE /api/staff/products/associations/123/
```

## Response Format

### Get Association Response (200 OK)
```json
{
  "id": 123,
  "product_type": 1,
  "product_type_title": "Electronics",
  "attribute": 5,
  "attribute_title": "Color",
  "is_filterable": true,
  "is_option_selector": false
}
```

### Update Association Response (200 OK)
```json
{
  "id": 123,
  "product_type": 1,
  "product_type_title": "Electronics",
  "attribute": 5,
  "attribute_title": "Color",
  "is_filterable": true,
  "is_option_selector": false
}
```

### Delete Association Response (204 No Content)
```
(Empty response body)
```

### Error Responses

#### Association not found (404 Not Found)
```json
{
  "error": "Association not found"
}
```

#### Validation errors (400 Bad Request)
```json
{
  "is_filterable": ["This field must be a boolean."]
}
```

## Python Examples

```python
import requests

# Get association
response = requests.get(
    '/api/staff/products/associations/123/',
    headers={'Authorization': 'Bearer <your-token>'}
)

# Update association
data = {
    "is_filterable": True,
    "is_option_selector": False
}

response = requests.patch(
    '/api/staff/products/associations/123/',
    json=data,
    headers={'Authorization': 'Bearer <your-token>'}
)

if response.status_code == 200:
    updated_association = response.json()
    print(f"Updated association: {updated_association}")
else:
    print(f"Error: {response.json()}")

# Delete association
response = requests.delete(
    '/api/staff/products/associations/123/',
    headers={'Authorization': 'Bearer <your-token>'}
)

if response.status_code == 204:
    print("Association deleted successfully")
```

## JavaScript Examples

```javascript
const manageAssociation = {
  // Get association
  async get(associationId) {
    const response = await fetch(`/api/staff/products/associations/${associationId}/`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.ok) {
      return await response.json();
    }
    throw new Error('Failed to get association');
  },

  // Update association
  async update(associationId, updates) {
    const response = await fetch(`/api/staff/products/associations/${associationId}/`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updates)
    });

    if (response.ok) {
      return await response.json();
    }
    throw new Error('Failed to update association');
  },

  // Delete association
  async delete(associationId) {
    const response = await fetch(`/api/staff/products/associations/${associationId}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 204) {
      return true;
    }
    throw new Error('Failed to delete association');
  }
};

// Usage examples
const association = await manageAssociation.get(123);
await manageAssociation.update(123, { is_filterable: true });
await manageAssociation.delete(123);
```

## Audit Logging

The endpoint automatically logs all updates with the following details:
- Action: `ASSOCIATION_UPDATE`
- Staff user who made the change
- Association ID and related product type/attribute IDs
- Before and after values for changed fields
- Request metadata (IP, user agent, etc.)

## Related Endpoints

- `GET /api/staff/products/associations/product_type_attributes/` - List associations
- `POST /api/staff/products/associations/save_association/` - Create new association
- `DELETE /api/staff/products/associations/delete_association/` - Delete association
