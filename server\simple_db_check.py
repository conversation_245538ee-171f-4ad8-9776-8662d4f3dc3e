from django.db import connection

with connection.cursor() as cursor:
    cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'order_order' AND column_name LIKE '%status%';")
    status_columns = cursor.fetchall()
    print("Status columns in order_order table:")
    for col in status_columns:
        print(f"  - {col[0]}")
    
    if not status_columns:
        print("  No status columns found!")
