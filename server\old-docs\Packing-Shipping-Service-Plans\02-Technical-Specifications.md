# Technical Specifications: Packing & Shipping Service

## 🔧 Database Schema Design

### ProductVariant Model Extensions
```sql
-- Migration: Add dimensions to ProductVariant
ALTER TABLE products_productvariant ADD COLUMN length DECIMAL(8,2) NOT NULL DEFAULT 0;
ALTER TABLE products_productvariant ADD COLUMN width DECIMAL(8,2) NOT NULL DEFAULT 0;
ALTER TABLE products_productvariant ADD COLUMN height DECIMAL(8,2) NOT NULL DEFAULT 0;
ALTER TABLE products_productvariant ADD COLUMN volume DECIMAL(12,4) NOT NULL DEFAULT 0;

-- Indexes for performance
CREATE INDEX idx_productvariant_volume ON products_productvariant(volume);
CREATE INDEX idx_productvariant_weight ON products_productvariant(weight);
CREATE INDEX idx_productvariant_dimensions ON products_productvariant(length, width, height);
```

### Shipping App Tables
```sql
-- Box configurations
CREATE TABLE shipping_box (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    internal_length DECIMAL(8,2) NOT NULL,
    internal_width DECIMAL(8,2) NOT NULL,
    internal_height DECIMAL(8,2) NOT NULL,
    max_weight DECIMAL(8,2) NOT NULL,
    cost DECIMAL(6,2) NOT NULL,
    volume DECIMAL(12,4) NOT NULL,
    is_mailer BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Packing rules
CREATE TABLE shipping_packingrule (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    min_weight DECIMAL(8,2) NULL,
    max_weight DECIMAL(8,2) NULL,
    min_volume DECIMAL(12,4) NULL,
    max_volume DECIMAL(12,4) NULL,
    preferred_box_id BIGINT REFERENCES shipping_box(id),
    force_mailer BOOLEAN DEFAULT FALSE,
    additional_cost DECIMAL(6,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Carriers
CREATE TABLE shipping_carrier (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    api_endpoint VARCHAR(200),
    api_key VARCHAR(255),
    base_cost DECIMAL(6,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Carrier services
CREATE TABLE shipping_carrierservice (
    id BIGSERIAL PRIMARY KEY,
    carrier_id BIGINT REFERENCES shipping_carrier(id),
    service_name VARCHAR(100) NOT NULL,
    service_code VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    estimated_days INTEGER DEFAULT 3
);
```

### Cart Model Extensions
```sql
-- Add shipping fields to cart
ALTER TABLE cart_cart ADD COLUMN packing_cost DECIMAL(6,2) DEFAULT 0;
ALTER TABLE cart_cart ADD COLUMN shipping_cost DECIMAL(6,2) DEFAULT 0;
ALTER TABLE cart_cart ADD COLUMN total_weight DECIMAL(8,2) DEFAULT 0;
ALTER TABLE cart_cart ADD COLUMN total_volume DECIMAL(12,4) DEFAULT 0;
ALTER TABLE cart_cart ADD COLUMN last_shipping_calculation TIMESTAMP NULL;
```

## 🏗️ Service Architecture

### 1. Packing Service Implementation
```python
# apps/shipping/services/packing.py
from dataclasses import dataclass
from typing import List, Optional
from decimal import Decimal
from py3dbp import Packer, Bin, Item
import logging

@dataclass
class PackedBox:
    box: 'Box'
    items: List['PackedItem']
    utilization: float
    total_weight: Decimal
    total_cost: Decimal

@dataclass
class PackingResult:
    boxes: List[PackedBox]
    total_cost: Decimal
    total_weight: Decimal
    total_volume: Decimal
    unpacked_items: List['CartItem']
    success: bool

class PackingService:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache_timeout = 3600
    
    def calculate_optimal_packaging(self, cart_items: List['CartItem']) -> PackingResult:
        """Main entry point for packaging calculation"""
        try:
            # Filter physical items
            physical_items = self._filter_physical_items(cart_items)
            if not physical_items:
                return PackingResult(
                    boxes=[], total_cost=Decimal('0'), total_weight=Decimal('0'),
                    total_volume=Decimal('0'), unpacked_items=[], success=True
                )
            
            # Apply packing rules
            rule_results = self._apply_packing_rules(physical_items)
            
            # Run 3D bin packing for remaining items
            packing_result = self._run_bin_packing(rule_results.remaining_items)
            
            # Combine results
            return self._combine_results(rule_results, packing_result)
            
        except Exception as e:
            self.logger.error(f"Packing calculation failed: {e}")
            return self._get_fallback_result(cart_items)
    
    def _filter_physical_items(self, cart_items):
        """Filter out digital products"""
        return [
            item for item in cart_items 
            if not item.product.is_digital and item.product_variant.weight > 0
        ]
    
    def _apply_packing_rules(self, items):
        """Apply configured packing rules"""
        rules = self._get_active_rules()
        processed_items = []
        remaining_items = list(items)
        
        for rule in rules.order_by('priority'):
            matching_items = []
            for item in remaining_items[:]:
                if self._item_matches_rule(item, rule):
                    matching_items.append(item)
                    remaining_items.remove(item)
            
            if matching_items:
                processed_items.extend(
                    self._process_rule_items(matching_items, rule)
                )
        
        return RuleResult(processed_items, remaining_items)
    
    def _run_bin_packing(self, items):
        """Execute 3D bin packing algorithm"""
        if not items:
            return PackingResult([], Decimal('0'), Decimal('0'), Decimal('0'), [], True)
        
        packer = Packer()
        boxes = self._get_available_boxes()
        
        # Add boxes as bins (sorted by cost efficiency)
        for box in sorted(boxes, key=lambda b: b.cost / b.volume):
            bin_obj = Bin(
                name=box.name,
                width=float(box.internal_width),
                height=float(box.internal_height),
                depth=float(box.internal_length),
                max_weight=float(box.max_weight)
            )
            packer.addBin(bin_obj)
        
        # Add items with quantity expansion
        for cart_item in items:
            variant = cart_item.product_variant
            for i in range(cart_item.quantity):
                item_obj = Item(
                    name=f"{variant.sku}_{i}",
                    width=float(variant.width),
                    height=float(variant.height),
                    depth=float(variant.length),
                    weight=float(variant.weight)
                )
                packer.addItem(item_obj)
        
        # Execute packing
        packer.pack()
        return self._process_packing_result(packer, boxes)
    
    def _process_packing_result(self, packer, available_boxes):
        """Process py3dbp results into our format"""
        packed_boxes = []
        total_cost = Decimal('0')
        total_weight = Decimal('0')
        unpacked_items = []
        
        # Process packed bins
        for bin_obj in packer.bins:
            if bin_obj.items:
                box = next(b for b in available_boxes if b.name == bin_obj.name)
                packed_items = self._group_packed_items(bin_obj.items)
                
                box_weight = sum(Decimal(str(item.weight)) for item in bin_obj.items)
                utilization = (sum(item.width * item.height * item.depth for item in bin_obj.items) / 
                             (bin_obj.width * bin_obj.height * bin_obj.depth)) * 100
                
                packed_box = PackedBox(
                    box=box,
                    items=packed_items,
                    utilization=utilization,
                    total_weight=box_weight,
                    total_cost=box.cost
                )
                
                packed_boxes.append(packed_box)
                total_cost += box.cost
                total_weight += box_weight
        
        # Handle unpacked items
        for item in packer.unfit_items:
            unpacked_items.append(self._find_cart_item_by_name(item.name))
        
        return PackingResult(
            boxes=packed_boxes,
            total_cost=total_cost,
            total_weight=total_weight,
            total_volume=sum(box.box.volume for box in packed_boxes),
            unpacked_items=unpacked_items,
            success=len(unpacked_items) == 0
        )
```

### 2. Shipping Service Implementation
```python
# apps/shipping/services/shipping.py
from abc import ABC, abstractmethod
from typing import List, Optional
import requests
from django.core.cache import cache

@dataclass
class ShippingRate:
    carrier_name: str
    service_name: str
    cost: Decimal
    estimated_days: int
    tracking_available: bool = True

class ShippingService:
    def __init__(self):
        self.carriers = self._load_active_carriers()
        self.cache_timeout = 300  # 5 minutes
    
    def calculate_shipping_cost(self, packing_result: PackingResult, 
                              destination_address: 'Address') -> Optional[ShippingRate]:
        """Calculate best shipping rate from available carriers"""
        cache_key = self._generate_cache_key(packing_result, destination_address)
        cached_rate = cache.get(cache_key)
        
        if cached_rate:
            return cached_rate
        
        best_rate = None
        for carrier in self.carriers:
            try:
                rate = carrier.get_shipping_rate(packing_result, destination_address)
                if not best_rate or rate.cost < best_rate.cost:
                    best_rate = rate
            except CarrierException as e:
                self.logger.warning(f"Carrier {carrier.name} failed: {e}")
                continue
        
        if best_rate:
            cache.set(cache_key, best_rate, self.cache_timeout)
        
        return best_rate or self._get_fallback_rate(packing_result)
    
    def _load_active_carriers(self):
        """Load and initialize active carriers"""
        from .carriers import get_carrier_instance
        
        carriers = []
        for carrier_config in Carrier.objects.filter(is_active=True):
            try:
                carrier_instance = get_carrier_instance(carrier_config)
                carriers.append(carrier_instance)
            except Exception as e:
                self.logger.error(f"Failed to load carrier {carrier_config.name}: {e}")
        
        return carriers
```

### 3. Carrier Interface & Posten Bring Implementation
```python
# apps/shipping/services/carriers/base.py
class BaseCarrier(ABC):
    def __init__(self, config: 'Carrier'):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{config.code}")
    
    @abstractmethod
    def get_shipping_rate(self, packing_result: PackingResult, 
                         destination: 'Address') -> ShippingRate:
        pass
    
    @abstractmethod
    def create_shipment(self, order: 'Order', packing_result: PackingResult) -> dict:
        pass
    
    @abstractmethod
    def track_shipment(self, tracking_number: str) -> dict:
        pass

# apps/shipping/services/carriers/posten_bring.py
class PostenBringCarrier(BaseCarrier):
    """Mock implementation for Posten Bring (Norway)"""
    
    def __init__(self, config):
        super().__init__(config)
        self.mock_rates = self._load_mock_rates()
        self.base_url = "https://api.bring.com/shippingguide/v2"
    
    def get_shipping_rate(self, packing_result: PackingResult, 
                         destination: 'Address') -> ShippingRate:
        """Calculate shipping rate using mock data"""
        total_weight = float(packing_result.total_weight)
        
        # Mock rate calculation based on weight brackets
        base_rate = self._calculate_base_rate(total_weight)
        
        # Add distance modifier (mock)
        distance_modifier = self._get_distance_modifier(destination)
        
        # Add box count modifier
        box_modifier = len(packing_result.boxes) * Decimal('2.50')
        
        total_cost = base_rate + distance_modifier + box_modifier
        
        return ShippingRate(
            carrier_name="Posten Bring",
            service_name="Standard Parcel",
            cost=total_cost,
            estimated_days=self._estimate_delivery_days(destination),
            tracking_available=True
        )
    
    def _calculate_base_rate(self, weight_grams: float) -> Decimal:
        """Mock rate calculation based on Norwegian postal rates"""
        # Based on Posten Bring 2024 rates (approximated)
        weight_brackets = [
            (250, Decimal('89.00')),    # Up to 250g - $89 NOK ≈ $8.50 USD
            (500, Decimal('109.00')),   # Up to 500g - $109 NOK ≈ $10.50 USD
            (1000, Decimal('129.00')),  # Up to 1kg - $129 NOK ≈ $12.50 USD
            (2000, Decimal('159.00')),  # Up to 2kg - $159 NOK ≈ $15.50 USD
            (5000, Decimal('199.00')),  # Up to 5kg - $199 NOK ≈ $19.50 USD
            (10000, Decimal('259.00')), # Up to 10kg - $259 NOK ≈ $25.50 USD
            (20000, Decimal('349.00')), # Up to 20kg - $349 NOK ≈ $34.50 USD
        ]
        
        for max_weight, cost_nok in weight_brackets:
            if weight_grams <= max_weight:
                # Convert NOK to USD (approximate rate: 1 USD = 10.5 NOK)
                return cost_nok / Decimal('10.5')
        
        # Over 20kg - calculate per kg
        excess_kg = (weight_grams - 20000) / 1000
        base_cost = Decimal('349.00') / Decimal('10.5')  # Base 20kg cost in USD
        additional_cost = excess_kg * Decimal('2.50')    # $2.50 per additional kg
        
        return base_cost + additional_cost
    
    def _get_distance_modifier(self, destination: 'Address') -> Decimal:
        """Mock distance-based pricing"""
        # Simplified: assume all domestic Norway shipping
        if destination.country.upper() == 'NO':
            return Decimal('0.00')  # Domestic
        elif destination.country.upper() in ['SE', 'DK', 'FI']:
            return Decimal('5.00')  # Nordic countries
        elif destination.country.upper() in ['DE', 'NL', 'BE', 'FR', 'GB']:
            return Decimal('12.00')  # Western Europe
        else:
            return Decimal('25.00')  # Rest of world
    
    def _estimate_delivery_days(self, destination: 'Address') -> int:
        """Estimate delivery time"""
        if destination.country.upper() == 'NO':
            return 2  # Domestic Norway
        elif destination.country.upper() in ['SE', 'DK', 'FI']:
            return 4  # Nordic
        elif destination.country.upper() in ['DE', 'NL', 'BE', 'FR', 'GB']:
            return 7  # Western Europe
        else:
            return 14  # International
```

## 🔄 Cart Integration Workflow

### Real-time Calculation Trigger Points
1. **Add item to cart** → Recalculate shipping
2. **Remove item from cart** → Recalculate shipping  
3. **Update item quantity** → Recalculate shipping
4. **Change shipping address** → Recalculate shipping

### Cart Service Integration
```python
# apps/cart/services.py
class CartService:
    def __init__(self):
        self.packing_service = PackingService()
        self.shipping_service = ShippingService()
    
    def add_item(self, cart, product_variant, quantity):
        """Add item and trigger shipping recalculation"""
        with transaction.atomic():
            cart_item = self._create_or_update_cart_item(cart, product_variant, quantity)
            self._recalculate_shipping(cart)
            return cart_item
    
    def _recalculate_shipping(self, cart):
        """Recalculate packing and shipping costs"""
        try:
            # Calculate optimal packaging
            packing_result = self.packing_service.calculate_optimal_packaging(
                cart.cart_items.select_related('product', 'product_variant').all()
            )
            
            # Calculate shipping cost
            shipping_address = cart.customer.get_default_shipping_address()
            if shipping_address:
                shipping_rate = self.shipping_service.calculate_shipping_cost(
                    packing_result, shipping_address
                )
            else:
                shipping_rate = None
            
            # Update cart
            cart.packing_cost = packing_result.total_cost
            cart.shipping_cost = shipping_rate.cost if shipping_rate else Decimal('0')
            cart.total_weight = packing_result.total_weight
            cart.total_volume = packing_result.total_volume
            cart.last_shipping_calculation = timezone.now()
            cart.save()
            
        except Exception as e:
            logger.error(f"Failed to recalculate shipping for cart {cart.id}: {e}")
            # Don't fail the cart operation, use fallback values
            cart.packing_cost = Decimal('5.00')  # Fallback packing cost
            cart.shipping_cost = Decimal('15.00')  # Fallback shipping cost
            cart.save()
```

## 📊 Performance Optimizations

### Database Optimizations
```python
# Optimized queries for cart calculations
def get_cart_with_shipping_data(cart_id):
    return Cart.objects.select_related(
        'customer__default_shipping_address__country'
    ).prefetch_related(
        'cart_items__product',
        'cart_items__product_variant',
        'cart_items__product__product_type'
    ).get(id=cart_id)

# Bulk operations for large carts
def bulk_update_cart_items(cart_items_data):
    CartItem.objects.bulk_update(
        cart_items_data, 
        ['quantity', 'updated_at'],
        batch_size=100
    )
```

### Caching Strategy
```python
# Redis cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Cache keys and timeouts
SHIPPING_CACHE_KEYS = {
    'boxes': 'shipping:boxes:v1',
    'rules': 'shipping:rules:v1', 
    'carriers': 'shipping:carriers:v1',
    'rates': 'shipping:rates:v1:{hash}',
}

SHIPPING_CACHE_TIMEOUTS = {
    'boxes': 3600,      # 1 hour - boxes don't change often
    'rules': 1800,      # 30 minutes - rules may change more frequently
    'carriers': 7200,   # 2 hours - carrier config is stable
    'rates': 300,       # 5 minutes - rates can fluctuate
}
```

This technical specification provides the detailed implementation blueprint for the packing and shipping service, ensuring optimal performance, maintainability, and scalability.
