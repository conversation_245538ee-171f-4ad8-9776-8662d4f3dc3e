#!/usr/bin/env python
"""
Check migration status for order app.
Run this with: python manage.py shell < check_migration_status.py
"""

from django.db import connection
from django.db.migrations.recorder import MigrationRecorder

def check_migration_status():
    """Check which migrations have been applied"""
    recorder = MigrationRecorder(connection)
    
    # Get all applied migrations for order app
    applied_migrations = recorder.applied_migrations()
    order_migrations = [m for m in applied_migrations if m[0] == 'order']
    
    print("=== Applied Order Migrations ===")
    for app, migration in sorted(order_migrations):
        print(f"{app}.{migration}")
    
    print(f"\nTotal order migrations applied: {len(order_migrations)}")
    
    # Check if our specific migrations exist
    key_migrations = [
        ('order', '0001_initial'),
        ('order', '0002_initial'), 
        ('order', '0003_alter_order_placed_at'),
        ('order', '0004_rename_delivery_status_to_order_status'),
        ('order', '0004_rename_delivery_status_order_order_status'),
    ]
    
    print("\n=== Key Migration Status ===")
    for app, migration in key_migrations:
        status = "✅ APPLIED" if (app, migration) in applied_migrations else "❌ NOT APPLIED"
        print(f"{app}.{migration}: {status}")

if __name__ == "__main__":
    check_migration_status()
