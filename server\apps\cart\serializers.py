from django.conf import settings
from django.core.exceptions import ValidationError
from rest_framework.serializers import ModelSerializer
from rest_framework import serializers
from django.db.models import Sum, F
from decimal import Decimal
from .models import Cart, CartItem
from apps.products.serializers import SimpleProductSerializer, SimpleProductVariantSerializer
from apps.products.models import ProductVariant, Product
from utils.shipping_cost import calculate_shipping_cost


class CartItemSerializer(ModelSerializer):
    product = SimpleProductSerializer()
    product_variant = SimpleProductVariantSerializer()
    qty_price = serializers.SerializerMethodField()
    extra_data = serializers.JSONField()

    # @extend_schema_field(Decimal)
    # def get_qty_price(self, cart_item: CartItem) -> Decimal:
    #     # qty_price = cart_item.quantity * cart_item.product_variant.price
    #     # return Decimal(qty_price)
    #     return cart_item.get_total_item_price()

    def get_qty_price(self, cart_item):
        return cart_item.get_total_item_price()

    class Meta:
        model = CartItem
        fields = ['id', 'product', 'product_variant', 'quantity', 'qty_price', 'extra_data']


class CartSerializer(ModelSerializer):
    id = serializers.UUIDField(read_only=True)
    cart_items = CartItemSerializer(many=True, read_only=True)
    total_price = serializers.SerializerMethodField()
    shipping_cost = serializers.SerializerMethodField()
    packing_cost = serializers.SerializerMethodField()
    total_volume = serializers.SerializerMethodField()
    grand_total = serializers.SerializerMethodField()
    cart_weight = serializers.SerializerMethodField()

    # def get_cart_weight(self, cart):
    #     return cart.get_cart_weight()

    def get_cart_weight(self, cart):
        # Use prefetched cart_items
        return sum(item.quantity * item.product_variant.weight for item in cart.cart_items.all())

    # Default to the highest cost for weights over 20000g

    # def get_total_price(self, cart):
    #     # return sum(item.quantity * item.price for item in cart.cart_items.all())
    #     return sum(item.get_total_item_price() for item in cart.cart_items.all())

    # def get_total_price(self, cart):
    #     return cart.cart_items.aggregate(total_price=Sum(F('quantity') * F('product_variant__price')))[
    #         'total_price'] or 0

    def get_total_price(self, cart):
        # Use prefetched cart_items with discounted prices
        return sum(item.get_total_item_price() for item in cart.cart_items.all())

    # def get_cart_weight(self, cart):
    #     return sum(item.quantity * item.product_variant.weight for item in cart.cart_items.all())

    def get_shipping_cost(self, cart):
        # Use the cart's calculated shipping_cost field
        return getattr(cart, 'shipping_cost', Decimal('0.00'))

    def get_grand_total(self, cart):
        return self.get_total_price(cart) + self.get_shipping_cost(cart)

    def get_packing_cost(self, cart):
        """Get packing cost from cart"""
        return getattr(cart, 'packing_cost', Decimal('0.00'))

    def get_total_volume(self, cart):
        """Get total volume from cart"""
        return getattr(cart, 'total_volume', Decimal('0.0000'))

    class Meta:
        model = Cart
        fields = ['id', 'cart_items', 'customer', 'cart_weight', 'total_price', 'shipping_cost',
                  'packing_cost', 'total_volume', 'grand_total', 'last_shipping_calculation']


class AddCartItemSerializer(ModelSerializer):
    product_id = serializers.IntegerField()
    # This querying is just for validation
    product_variant = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all())
    extra_data = serializers.JSONField(default={})

    # Validating the product_id(pk) field of the passed object to the serializer
    def validate_product_id(self, value):
        if not Product.objects.filter(pk=value).exists():
            raise serializers.ValidationError('No product with given ID was found')
        return value

    # Check cart weight before adding more items
    def validate(self, data):
        cart_id = self.context['cart_id']
        product_variant = data['product_variant']
        quantity = data['quantity']

        cart = Cart.objects.get(id=cart_id)
        current_weight = cart.get_cart_weight()
        additional_weight = product_variant.weight * quantity

        if current_weight + additional_weight > 20000:
            raise ValidationError("Adding this item would exceed the maximum weight limit of 20,000 grams.")

        return data

    # Validating the product_variant_id(pk) field of the passed object to the serializer
    # def validate_product_variant_id(self, value):
    #     if not ProductVariant.objects.filter(pk=value).exists():
    #         raise serializers.ValidationError('No product variant with given ID was found')
    #     return value

    # If item exist in the cart increase the quantity or create a cart and add the item
    def save(self, **kwargs):
        cart_id = self.context['cart_id']
        product_id = self.validated_data['product_id']
        product_variant = self.validated_data['product_variant']
        quantity = self.validated_data['quantity']
        extra_data = self.validated_data['extra_data']

        # Calculate the price to use (discounted or regular)
        price_to_use = product_variant.get_discounted_price() or product_variant.price

        # Separate Query and Command (but this isn't working)
        # Client get a 404 error when try to get a cart that doesn't exist on the server
        try:
            cart = Cart.objects.get(id=cart_id)  # Query
        except Cart.DoesNotExist:
            cart = Cart.objects.create(id=cart_id)  # Command

        # Update quantity if item exists in the cart
        try:
            cart_item = CartItem.objects.get(
                cart=cart,
                product_id=product_id,
                product_variant=product_variant
            )
            cart_item.quantity += quantity
            # cart_item.price = price_to_use  # Update price in case it has changed
            cart_item.save()
            self.instance = cart_item
        except CartItem.DoesNotExist:
            # Create a new cart item
            self.instance = CartItem.objects.create(
                cart=cart,
                product_id=product_id,
                product_variant=product_variant,
                quantity=quantity,
                # price=price_to_use,  # Set price at creation
                extra_data=extra_data
            )

        # Trigger shipping recalculation after adding/updating item
        self._recalculate_shipping(cart)

    def _recalculate_shipping(self, cart):
        """Recalculate shipping costs for the cart"""
        try:
            from apps.shipping.services import CartShippingService
            cart_shipping_service = CartShippingService()
            cart_shipping_service.recalculate_cart_shipping(cart, force_recalculate=True)
        except Exception as e:
            # Log error but don't fail the cart operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to recalculate shipping for cart {cart.id}: {e}")

    class Meta:
        model = CartItem
        fields = ['id', 'product_id', 'product_variant', 'quantity', 'extra_data']


class UpdateCartItemSerializer(ModelSerializer):
    quantity = serializers.IntegerField()

    def validate_quantity(self, value):
        if value < 1:
            raise serializers.ValidationError("Quantity must be at least 1.")
        cart_item = self.instance
        cart = cart_item.cart
        product_variant = cart_item.product_variant

        # Calculate the weight difference
        weight_difference = (value - cart_item.quantity) * product_variant.weight

        # Check if the new total weight would exceed the limit
        if cart.get_cart_weight() + weight_difference > settings.MAX_CART_WEIGHT:
            raise serializers.ValidationError(
                f"Updating this item would exceed the maximum weight limit of {settings.MAX_CART_WEIGHT} grams."
            )

        return value

    def save(self, **kwargs):
        """Save and trigger shipping recalculation"""
        instance = super().save(**kwargs)

        # Trigger shipping recalculation after updating quantity
        try:
            from apps.shipping.services import CartShippingService
            cart_shipping_service = CartShippingService()
            cart_shipping_service.recalculate_cart_shipping(instance.cart, force_recalculate=True)
        except Exception as e:
            # Log error but don't fail the cart operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to recalculate shipping for cart {instance.cart.id}: {e}")

        return instance

    class Meta:
        model = CartItem
        fields = ['quantity']
