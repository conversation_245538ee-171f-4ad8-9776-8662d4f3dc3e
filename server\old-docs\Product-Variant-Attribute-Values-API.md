# Product Variant Attribute Values API

## Overview
This document describes the new API endpoint for retrieving all associated attribute values for a specific product variant in the staff application.

## Endpoint Details

### Get Product Variant Attribute Values
**URL:** `/staff/products/variants/{variant_id}/attribute_values/`  
**Method:** `GET`  
**Permission:** `CanManageProducts`  
**Description:** Retrieves all attribute values associated with a specific product variant.

### URL Parameters
- `variant_id` (integer, required): The ID of the product variant

### Query Parameters
- `is_active` (boolean, optional): Filter by active status (`true` or `false`)
- `attribute` (integer, optional): Filter by specific attribute ID

### Response Format

```json
{
    "variant_sku": "TEST-SKU-001",
    "variant_id": 123,
    "product_title": "Sample Product",
    "attribute_values_count": 2,
    "attribute_values": [
        {
            "id": 1,
            "attribute_value": {
                "id": 10,
                "attribute_value": "Red",
                "attribute": 5,
                "is_active": true
            },
            "attribute": {
                "id": 5,
                "title": "Color",
                "description": "Product color attribute"
            },
            "is_active": true,
            "order": 1
        },
        {
            "id": 2,
            "attribute_value": {
                "id": 15,
                "attribute_value": "Large",
                "attribute": 6,
                "is_active": true
            },
            "attribute": {
                "id": 6,
                "title": "Size",
                "description": "Product size attribute"
            },
            "is_active": true,
            "order": 2
        }
    ]
}
```

### Response Fields

#### Root Level
- `variant_sku` (string): The SKU of the product variant
- `variant_id` (integer): The ID of the product variant
- `product_title` (string): The title of the parent product
- `attribute_values_count` (integer): Total count of attribute values
- `attribute_values` (array): Array of attribute value associations

#### Attribute Value Association Object
- `id` (integer): Association ID
- `attribute_value` (object): The attribute value details
- `attribute` (object): The attribute details
- `is_active` (boolean): Whether the association is active
- `order` (integer): Display order for this attribute value

### Example Requests

#### Get all attribute values for a variant
```
GET /staff/products/variants/123/attribute_values/
```

#### Get only active attribute values
```
GET /staff/products/variants/123/attribute_values/?is_active=true
```

#### Get attribute values for a specific attribute
```
GET /staff/products/variants/123/attribute_values/?attribute=5
```

#### Combine filters
```
GET /staff/products/variants/123/attribute_values/?is_active=true&attribute=5
```

### HTTP Status Codes
- `200 OK`: Successfully retrieved attribute values
- `404 Not Found`: Product variant not found
- `403 Forbidden`: Insufficient permissions
- `401 Unauthorized`: Authentication required

### Use Cases
1. **Admin Interface**: Display all attributes associated with a product variant
2. **Bulk Operations**: Get current associations before making changes
3. **Validation**: Check existing attributes before adding new ones
4. **Reporting**: Generate reports on product variant attributes

### Related Endpoints
- `GET /staff/products/variants/` - List all product variants
- `GET /staff/products/variant-attribute-values/` - Manage variant-attribute associations
- `POST /staff/products/variant-attribute-values/bulk_associate/` - Bulk associate attributes

### Implementation Notes
- Results are ordered by `order` field first, then by attribute title
- Uses `ProductVariantAttributeValueDetailSerializer` for detailed response
- Includes proper select_related optimization for database queries
- Supports filtering by both active status and specific attributes
