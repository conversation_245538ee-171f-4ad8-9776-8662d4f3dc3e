from django.core.validators import MinValueValidator
from django.db import models
from datetime import timedelta
from django.utils.timezone import now
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from decimal import Decimal
from apps.payments.models import PaymentOption
from apps.customers.models import Customer, Address
from apps.products.models import Product, ProductVariant


def get_delivery_date():
    return now().date() + timedelta(days=5)


class Order(models.Model):
    PAYMENT_STATUS = (
        ('Pending', 'Pending'),
        ('Paid', 'Paid'),
        ('Failed', 'Failed'),
    )
    ORDER_STATUS = (
        ('Pending', 'Pending'),
        ('Processing', 'Processing'),
        ('Dispatched', 'Dispatched'),  # Store handed over the package to the courier
        ('Delivered', 'Delivered'),
    )
    payment_status = models.CharField(
        max_length=8, choices=PAYMENT_STATUS, default='Pending')
    order_status = models.CharField(
        max_length=10, choices=ORDER_STATUS, default='Pending')
    payment_method = models.ForeignKey(PaymentOption, on_delete=models.PROTECT)
    customer = models.ForeignKey(Customer, on_delete=models.PROTECT)
    # Remove Null True here
    selected_address = models.ForeignKey(Address, on_delete=models.PROTECT, null=True)
    shipping_cost = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('15'))],
        default=15
    )
    subtotal = models.DecimalField(max_digits=6, decimal_places=2, null=True)  # Total of item prices
    total_weight = models.FloatField(null=True)  # in grams
    placed_at = models.DateTimeField(auto_now_add=True, db_index=True)
    # Remove Null True here
    total = models.DecimalField(max_digits=6, decimal_places=2, null=True)

    payment_intent_id = models.CharField(max_length=255, blank=True, null=True)  # New field for Stripe

    # delivery_date = models.DateField(null=True, blank=True, default=get_delivery_date)

    # class Meta:
    #     permissions = [
    #         ('cancel_order', 'Authorized to cancel orders')
    #     ]

    def __str__(self):
        return 'Orders'


class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='ordered_items')
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    product = models.ForeignKey(
        Product, on_delete=models.PROTECT, related_name='order_items')
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE)
    quantity = models.PositiveSmallIntegerField()
    extra_data = JSONField(default=dict, blank=True, null=True)

    def __str__(self):
        return f'{self.product.title}'

    # When someone created an order with product variant, the stock_qty has to be updated
    # sku = models.CharField(max_length=50)


# payments/models.py

class BraintreeTransaction(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    transaction_id = models.CharField(max_length=100)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20)
    payment_method = models.CharField(max_length=20)  # 'paypal' or 'card'
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
