from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.db.models import Sum, F
from decimal import Decimal
from uuid import uuid4
from apps.products.models import Product, ProductVariant
from apps.customers.models import Customer


class Cart(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4)
    created_at = models.DateTimeField(auto_now_add=True)
    customer = models.ForeignKey(
        Customer, on_delete=models.CASCADE, null=True, blank=True)

    # Shipping-related fields
    packing_cost = models.DecimalField(
        max_digits=6, decimal_places=2, default=Decimal('0.00'),
        help_text="Total packing cost for cart items"
    )
    shipping_cost = models.DecimalField(
        max_digits=6, decimal_places=2, default=Decimal('0.00'),
        help_text="Shipping cost for cart"
    )
    total_weight = models.DecimalField(
        max_digits=8, decimal_places=2, default=Decimal('0.00'),
        help_text="Total weight in grams"
    )
    total_volume = models.DecimalField(
        max_digits=12, decimal_places=4, default=Decimal('0.0000'),
        help_text="Total volume in cubic centimeters"
    )
    last_shipping_calculation = models.DateTimeField(
        null=True, blank=True,
        help_text="Last time shipping was calculated"
    )

    # def get_cart_weight(self):
    #     return sum(
    #         item.quantity * item.product_variant.weight
    #         for item in self.cart_items.all()
    #     )

    def get_cart_weight(self):
        return sum(
            item.quantity * item.product_variant.weight
            for item in self.cart_items.all()
        ) or 0  # Add or 0 to handle empty carts

    # def get_cart_weight(self):
    #     return self.cart_items.aggregate(total_weight=Sum(F('quantity') * F('product_variant__weight')))[
    #         'total_weight'] or 0

    def __str__(self):
        return f'Cart of {self.customer or "Anonymous User"}'


class CartItem(models.Model):
    cart = models.ForeignKey(Cart, on_delete=models.CASCADE, related_name='cart_items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    product_variant = models.ForeignKey(ProductVariant, on_delete=models.CASCADE)
    quantity = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1)]
    )
    # price = models.DecimalField(max_digits=10, decimal_places=2)  # Store price here
    extra_data = JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True, editable=False)
    updated_at = models.DateTimeField(auto_now=True, editable=False)

    # def get_total_item_price(self):
    #     discounted_price = self.product_variant.get_discounted_price()
    #     price = discounted_price or self.product_variant.price
    #     return Decimal(str(price)) * self.quantity

    def get_total_item_price(self):
        # Use prefetched active_discounts
        active_discounts = getattr(self.product_variant, 'active_discounts', [])
        price = (active_discounts[0].apply_discount(self.product_variant.price)
                 if active_discounts else self.product_variant.price)
        return Decimal(str(price)) * self.quantity

    class Meta:
        verbose_name = "Cart Items of Cart"
        # unique_together = ('cart', 'product')
        unique_together = ('cart', 'product', 'product_variant')

    def __str__(self):
        return f'{self.cart_id}'
