# Packing & Shipping Service Implementation Plan

## 📋 Overview

This plan implements a comprehensive packing and shipping service with rule-based box selection, real-time shipping calculations, and multi-carrier support. The system will be API-first with Django Admin configuration capabilities.

## 🎯 Core Requirements

- **Dimensions**: ProductVariant gets length, width, height (cm), weight (grams)
- **Real-time Calculations**: Shipping/packing fees calculated during cart operations
- **Configurable Rules**: Packing rules accessible via API and Django Admin
- **Multi-carrier Support**: Modular carrier system (starting with Posten Bring mocks)
- **3D Bin Packing**: Using py3dbp library for optimal packaging
- **Caching**: Redis for box configurations and shipping rates
- **Currency**: USD as base currency
- **Digital Products**: No shipping/packing fees

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Cart Service  │────│ Packing Service │────│ Shipping Service│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐              ┌───▼───┐              ┌────▼────┐
    │ Cart    │              │ Box   │              │Carrier  │
    │ Models  │              │Config │              │Adapters │
    └─────────┘              └───────┘              └─────────┘
```

## 📊 Phase 1: Model Updates & Core Infrastructure

### 1.1 ProductVariant Model Extensions
```python
# Add to ProductVariant model:
length = models.DecimalField(max_digits=8, decimal_places=2, help_text="Length in cm")
width = models.DecimalField(max_digits=8, decimal_places=2, help_text="Width in cm") 
height = models.DecimalField(max_digits=8, decimal_places=2, help_text="Height in cm")
volume = models.DecimalField(max_digits=12, decimal_places=4, editable=False, help_text="Computed volume in cm³")

def calculate_volume(self):
    return self.length * self.width * self.height

def save(self, *args, **kwargs):
    self.volume = self.calculate_volume()
    super().save(*args, **kwargs)
```

### 1.2 New Shipping App Structure
```
apps/shipping/
├── __init__.py
├── models/
│   ├── __init__.py
│   ├── boxes.py          # Box configurations
│   ├── carriers.py       # Carrier configurations
│   └── rules.py          # Packing rules
├── services/
│   ├── __init__.py
│   ├── packing.py        # 3D bin packing logic
│   ├── shipping.py       # Shipping rate calculations
│   └── carriers/
│       ├── __init__.py
│       ├── base.py       # Abstract carrier interface
│       └── posten_bring.py # Posten Bring implementation
├── serializers.py
├── views.py
├── admin.py
└── urls.py
```

### 1.3 Core Models

#### Box Model
```python
class Box(models.Model):
    name = models.CharField(max_length=100)
    internal_length = models.DecimalField(max_digits=8, decimal_places=2)  # cm
    internal_width = models.DecimalField(max_digits=8, decimal_places=2)   # cm
    internal_height = models.DecimalField(max_digits=8, decimal_places=2)  # cm
    max_weight = models.DecimalField(max_digits=8, decimal_places=2)       # grams
    cost = models.DecimalField(max_digits=6, decimal_places=2)             # USD
    volume = models.DecimalField(max_digits=12, decimal_places=4, editable=False)
    is_mailer = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
```

#### PackingRule Model
```python
class PackingRule(models.Model):
    name = models.CharField(max_length=100)
    priority = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    
    # Conditions
    min_weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    max_weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    min_volume = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    max_volume = models.DecimalField(max_digits=12, decimal_places=4, null=True, blank=True)
    product_types = models.ManyToManyField('products.ProductType', blank=True)
    
    # Actions
    preferred_box = models.ForeignKey(Box, on_delete=models.CASCADE, null=True, blank=True)
    force_mailer = models.BooleanField(default=False)
    additional_cost = models.DecimalField(max_digits=6, decimal_places=2, default=0)
```

#### Carrier Model
```python
class Carrier(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20, unique=True)
    is_active = models.BooleanField(default=True)
    api_endpoint = models.URLField(blank=True)
    api_key = models.CharField(max_length=255, blank=True)
    base_cost = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    
class CarrierService(models.Model):
    carrier = models.ForeignKey(Carrier, on_delete=models.CASCADE)
    service_name = models.CharField(max_length=100)
    service_code = models.CharField(max_length=50)
    is_active = models.BooleanField(default=True)
```

## 📊 Phase 2: Service Layer Implementation

### 2.1 Packing Service with py3dbp
```python
from py3dbp import Packer, Bin, Item

class PackingService:
    def __init__(self):
        self.cache_timeout = 3600  # 1 hour
        
    def calculate_optimal_packaging(self, cart_items):
        """Calculate optimal packaging for cart items using 3D bin packing"""
        physical_items = self._filter_physical_items(cart_items)
        if not physical_items:
            return PackingResult(boxes=[], total_cost=0, items=[])
            
        boxes = self._get_available_boxes()
        packing_result = self._run_bin_packing(physical_items, boxes)
        
        return packing_result
    
    def _run_bin_packing(self, items, boxes):
        """Execute 3D bin packing algorithm"""
        packer = Packer()
        
        # Add boxes as bins
        for box in boxes:
            bin_obj = Bin(
                name=box.name,
                width=float(box.internal_width),
                height=float(box.internal_height), 
                depth=float(box.internal_length),
                max_weight=float(box.max_weight)
            )
            packer.addBin(bin_obj)
        
        # Add items
        for cart_item in items:
            for _ in range(cart_item.quantity):
                item_obj = Item(
                    name=f"{cart_item.product_variant.sku}",
                    width=float(cart_item.product_variant.width),
                    height=float(cart_item.product_variant.height),
                    depth=float(cart_item.product_variant.length),
                    weight=float(cart_item.product_variant.weight)
                )
                packer.addItem(item_obj)
        
        packer.pack()
        return self._process_packing_result(packer)
```

### 2.2 Shipping Service with Carrier Abstraction
```python
class ShippingService:
    def __init__(self):
        self.carriers = self._load_carriers()
    
    def calculate_shipping_cost(self, packing_result, destination_address):
        """Calculate shipping cost using active carriers"""
        best_rate = None
        
        for carrier in self.carriers:
            try:
                rate = carrier.get_shipping_rate(packing_result, destination_address)
                if not best_rate or rate.total_cost < best_rate.total_cost:
                    best_rate = rate
            except CarrierException as e:
                logger.warning(f"Carrier {carrier.name} failed: {e}")
                continue
        
        return best_rate or self._get_fallback_rate(packing_result)
```

### 2.3 Carrier Interface
```python
class BaseCarrier(ABC):
    @abstractmethod
    def get_shipping_rate(self, packing_result, destination):
        pass
    
    @abstractmethod
    def create_shipment(self, order, packing_result):
        pass

class PostenBringCarrier(BaseCarrier):
    def __init__(self):
        self.mock_rates = self._load_mock_rates()
    
    def get_shipping_rate(self, packing_result, destination):
        """Mock implementation for Posten Bring"""
        total_weight = sum(box.total_weight for box in packing_result.boxes)
        base_rate = self._calculate_mock_rate(total_weight, destination)
        
        return ShippingRate(
            carrier_name="Posten Bring",
            service_name="Standard",
            cost=base_rate,
            estimated_days=3
        )
```

## 📊 Phase 3: Cart Integration

### 3.1 Cart Model Updates
```python
# Add to Cart model:
packing_cost = models.DecimalField(max_digits=6, decimal_places=2, default=0)
shipping_cost = models.DecimalField(max_digits=6, decimal_places=2, default=0)
total_weight = models.DecimalField(max_digits=8, decimal_places=2, default=0)
total_volume = models.DecimalField(max_digits=12, decimal_places=4, default=0)
```

### 3.2 Real-time Cart Updates
```python
class CartService:
    def add_item(self, cart, product_variant, quantity):
        """Add item and recalculate shipping"""
        cart_item = self._add_cart_item(cart, product_variant, quantity)
        self._recalculate_cart_totals(cart)
        return cart_item
    
    def _recalculate_cart_totals(self, cart):
        """Recalculate packing and shipping costs"""
        packing_result = PackingService().calculate_optimal_packaging(cart.cart_items.all())
        shipping_rate = ShippingService().calculate_shipping_cost(packing_result, cart.customer.default_address)
        
        cart.packing_cost = packing_result.total_cost
        cart.shipping_cost = shipping_rate.cost if shipping_rate else 0
        cart.total_weight = packing_result.total_weight
        cart.total_volume = packing_result.total_volume
        cart.save()
```

## 📊 Phase 4: API & Admin Interface

### 4.1 API Endpoints
```
GET  /api/shipping/boxes/              # List available boxes
POST /api/shipping/boxes/              # Create box (admin)
GET  /api/shipping/rules/              # List packing rules
POST /api/shipping/rules/              # Create rule (admin)
GET  /api/shipping/carriers/           # List carriers
POST /api/shipping/calculate/          # Calculate shipping for cart
```

### 4.2 Django Admin Configuration
- Box management with volume auto-calculation
- Packing rule builder with condition/action interface
- Carrier configuration with service management
- Shipping rate testing tools

## 📊 Phase 5: Caching & Performance

### 5.1 Redis Caching Strategy
```python
# Cache keys
CACHE_KEYS = {
    'boxes': 'shipping:boxes:active',
    'rules': 'shipping:rules:active',
    'carriers': 'shipping:carriers:active',
    'rates': 'shipping:rates:{carrier}:{weight}:{destination_hash}'
}

# Cache timeouts
CACHE_TIMEOUTS = {
    'boxes': 3600,      # 1 hour
    'rules': 1800,      # 30 minutes  
    'carriers': 7200,   # 2 hours
    'rates': 300        # 5 minutes
}
```

### 5.2 Performance Optimizations
- Bulk database operations for cart calculations
- Async carrier API calls with timeout handling
- Database indexing on frequently queried fields
- Query optimization with select_related/prefetch_related

## 📊 Phase 6: Testing Strategy

### 6.1 Unit Tests
- Model validation and calculations
- Service layer logic
- Carrier implementations
- Cache functionality

### 6.2 Integration Tests  
- Cart update workflows
- API endpoint functionality
- Admin interface operations
- End-to-end shipping calculations

### 6.3 Performance Tests
- High-volume cart operations
- Concurrent shipping calculations
- Cache performance under load
- Database query optimization

## 🚀 Implementation Timeline

**Week 1**: Phase 1 - Models and migrations
**Week 2**: Phase 2 - Service layer implementation  
**Week 3**: Phase 3 - Cart integration
**Week 4**: Phase 4 - API and admin interfaces
**Week 5**: Phase 5 - Caching and performance
**Week 6**: Phase 6 - Testing and optimization

## 📋 Success Criteria

- ✅ Real-time shipping calculation in cart
- ✅ Optimal box selection using 3D bin packing
- ✅ Configurable packing rules via admin
- ✅ Multi-carrier support with fallback
- ✅ Sub-second response times for cart updates
- ✅ 99.9% uptime for shipping calculations
- ✅ Comprehensive test coverage (>90%)

This plan provides a solid foundation for implementing the packing and shipping service. Each phase builds upon the previous one, ensuring a systematic and maintainable implementation.
