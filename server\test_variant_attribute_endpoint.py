#!/usr/bin/env python
"""
Test script for the new Product Variant Attribute Values endpoint
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pc_hardware.settings.development')
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from rest_framework.test import APIClient
from rest_framework import status
from model_bakery import baker

from apps.staff.products.models import (
    ProductProxy, CategoryProxy, BrandProxy, ProductTypeProxy, 
    AttributeProxy, AttributeValueProxy, ProductVariantProxy, 
    ProductVariantAttributeValueProxy
)

User = get_user_model()

def create_test_data():
    """Create test data for the endpoint"""
    print("Creating test data...")
    
    # Create staff user with permissions
    staff_user = baker.make(User, is_staff=True, email='<EMAIL>')
    
    # Create Product Manager group with permissions
    product_manager_group, created = Group.objects.get_or_create(name='Product Manager')
    staff_user.groups.add(product_manager_group)
    
    # Add basic permissions
    permissions = Permission.objects.filter(
        content_type__app_label='products',
        codename__in=['view_product', 'view_productvariant', 'view_attribute', 'view_attributevalue']
    )
    product_manager_group.permissions.set(permissions)
    
    # Create test data
    category = baker.make(CategoryProxy, title='Test Category')
    brand = baker.make(BrandProxy, title='Test Brand')
    product_type = baker.make(ProductTypeProxy, title='Test Type')
    
    # Create product and variant
    product = baker.make(
        ProductProxy,
        title='Test Product',
        category=category,
        brand=brand,
        product_type=product_type
    )
    variant = baker.make(
        ProductVariantProxy,
        product=product,
        sku='TEST-SKU-001',
        price=99.99,
        stock_qty=10
    )
    
    # Create attributes and attribute values
    color_attribute = baker.make(AttributeProxy, title='Color')
    size_attribute = baker.make(AttributeProxy, title='Size')
    
    red_value = baker.make(AttributeValueProxy, attribute=color_attribute, attribute_value='Red')
    large_value = baker.make(AttributeValueProxy, attribute=size_attribute, attribute_value='Large')
    
    # Create associations
    color_association = baker.make(
        ProductVariantAttributeValueProxy,
        product_variant=variant,
        attribute_value=red_value,
        is_active=True,
        order=1
    )
    size_association = baker.make(
        ProductVariantAttributeValueProxy,
        product_variant=variant,
        attribute_value=large_value,
        is_active=True,
        order=2
    )
    
    print(f"Created variant with ID: {variant.id}")
    print(f"Created {ProductVariantAttributeValueProxy.objects.filter(product_variant=variant).count()} associations")
    
    return staff_user, variant

def test_endpoint():
    """Test the new endpoint"""
    print("Testing the endpoint...")
    
    # Create test data
    staff_user, variant = create_test_data()
    
    # Create API client
    client = APIClient()
    client.force_authenticate(user=staff_user)
    
    # Test the endpoint
    url = f'/staff/products/variants/{variant.id}/attribute_values/'
    print(f"Testing URL: {url}")
    
    response = client.get(url)
    
    print(f"Response status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print("Response data:")
        print(f"  Variant SKU: {data.get('variant_sku')}")
        print(f"  Variant ID: {data.get('variant_id')}")
        print(f"  Product Title: {data.get('product_title')}")
        print(f"  Attribute Values Count: {data.get('attribute_values_count')}")
        print(f"  Number of attribute values: {len(data.get('attribute_values', []))}")
        
        for i, av in enumerate(data.get('attribute_values', [])):
            print(f"  Attribute Value {i+1}:")
            print(f"    ID: {av.get('id')}")
            print(f"    Attribute: {av.get('attribute', {}).get('title')}")
            print(f"    Value: {av.get('attribute_value', {}).get('attribute_value')}")
            print(f"    Order: {av.get('order')}")
            print(f"    Active: {av.get('is_active')}")
    else:
        print(f"Error response: {response.content}")
    
    # Test with filters
    print("\nTesting with is_active filter...")
    response = client.get(f'{url}?is_active=true')
    print(f"Filtered response status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Filtered count: {data.get('attribute_values_count')}")

if __name__ == '__main__':
    test_endpoint()
    print("Test completed!")
