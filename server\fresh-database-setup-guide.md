# Fresh Database Setup Guide

## Overview
This guide will help you completely reset your database and migrations for a clean start. This is safe for test/development databases but **NEVER do this in production**.

## Prerequisites
- ✅ This is a test/development database (no production data)
- ✅ You have backups if needed
- ✅ You're prepared to recreate any test data

## Step-by-Step Process

### Step 1: Stop the Development Server
```bash
# Stop Django development server if running
# Ctrl+C in the terminal where it's running
```

### Step 2: Delete All Migration Files
```bash
# Run the delete migrations script
python scripts/delete_migrations.py
```

This should remove all migration files except `__init__.py` files in each app's migrations folder.

### Step 3: Drop the Database

#### Option A: Using Django Management Command (if available)
```bash
python manage.py dbshell
```
Then in the PostgreSQL shell:
```sql
DROP DATABASE your_database_name;
CREATE DATABASE your_database_name;
\q
```

#### Option B: Using PostgreSQL Command Line
```bash
# Replace 'your_database_name' with your actual database name
dropdb your_database_name
createdb your_database_name
```

#### Option C: Using pgAdmin or Database GUI
- Connect to your PostgreSQL server
- Right-click your database → Delete/Drop
- Create a new database with the same name

### Step 4: Clear Django's Migration Tracking
```bash
# This removes Django's internal migration tracking
# (Optional, since we're dropping the database anyway)
rm -rf apps/*/migrations/__pycache__/
```

### Step 5: Create Fresh Migrations
```bash
# Create new initial migrations for all apps
python manage.py makemigrations
```

This will create new `0001_initial.py` files for all your apps with the current model state.

### Step 6: Apply Fresh Migrations
```bash
# Apply all migrations to the fresh database
python manage.py migrate
```

### Step 7: Create Superuser
```bash
# Create a new superuser account
python manage.py createsuperuser
```

### Step 8: Load Initial Data (if you have fixtures)
```bash
# If you have data fixtures, load them
python manage.py loaddata your_fixture_file.json
```

### Step 9: Test the Application
```bash
# Start the development server
python manage.py runserver
```

Test key functionality:
- ✅ Admin panel access
- ✅ Staff API endpoints
- ✅ Order creation and management
- ✅ Product management

## Verification Checklist

After completing the reset:

### Database Structure
- [ ] All tables created successfully
- [ ] `order_order` table has `order_status` column (not `delivery_status`)
- [ ] All foreign key relationships intact

### Migration Status
```bash
# Check that all migrations are applied
python manage.py showmigrations
```
All should show `[X]` (applied).

### API Endpoints
Test the endpoint that was originally failing:
```bash
# Test staff orders API
curl -X GET "http://localhost:8000/api/staff/orders/orders/" \
  -H "Authorization: Bearer <your-token>"
```

### Model Functionality
```bash
python manage.py shell -c "
from apps.order.models import Order
from apps.staff.orders.models import OrderProxy
print('Testing models...')
print(f'Order fields: {[f.name for f in Order._meta.fields]}')
print('✅ Models work correctly!')
"
```

## What This Fixes

✅ **Field Name Conflicts**: No more `delivery_status` vs `order_status` confusion
✅ **Migration Dependencies**: Clean dependency chain across all apps
✅ **Database Schema**: Matches current model definitions exactly
✅ **Staff API**: Should work without column errors
✅ **Future Migrations**: No more conflicts when running `makemigrations`

## Rollback Plan

If something goes wrong:
1. Restore database from backup (if you have one)
2. Restore migration files from git history
3. Or repeat this process with fixes

## Notes

- **Development Only**: Never do this on production databases
- **Data Loss**: All existing data will be lost
- **Test Data**: You'll need to recreate any test orders, products, etc.
- **Superuser**: You'll need to create a new superuser account
- **Settings**: Make sure your database settings in `settings.py` are correct

## Expected Results

After this process:
- Clean migration history starting from scratch
- Database schema exactly matching your current models
- No migration conflicts or dependency issues
- Staff API working correctly
- Consistent field naming throughout the codebase
